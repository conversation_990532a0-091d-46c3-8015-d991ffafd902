"""
BlendPro UI Style Guide
======================

This module contains style constants and utilities for consistent UI design
across the BlendPro addon interface.
"""

import bpy

class BlendProColors:
    """Color constants for BlendPro UI elements"""
    
    # Primary colors
    PRIMARY = (0.2, 0.4, 0.8, 1.0)      # Blue
    SECONDARY = (0.3, 0.7, 0.3, 1.0)    # Green
    ACCENT = (0.9, 0.5, 0.1, 1.0)       # Orange
    
    # Status colors
    SUCCESS = (0.2, 0.8, 0.2, 1.0)      # Green
    WARNING = (0.9, 0.7, 0.1, 1.0)      # Yellow
    ERROR = (0.9, 0.2, 0.2, 1.0)        # Red
    INFO = (0.4, 0.6, 0.9, 1.0)         # Light Blue
    
    # Neutral colors
    BACKGROUND = (0.15, 0.15, 0.15, 1.0) # Dark Gray
    SURFACE = (0.2, 0.2, 0.2, 1.0)      # Medium Gray
    TEXT = (0.9, 0.9, 0.9, 1.0)         # Light Gray
    MUTED = (0.6, 0.6, 0.6, 1.0)        # Muted Gray

class BlendProIcons:
    """Icon constants for consistent iconography"""
    
    # Tab icons
    BASIC = 'PREFERENCES'
    AGENTS = 'USER'
    VISION = 'CAMERA_DATA'
    PERFORMANCE = 'SYSTEM'
    ADVANCED = 'SETTINGS'
    
    # Feature icons
    API = 'WORLD'
    AI_BEHAVIOR = 'SETTINGS'
    FEATURES = 'MODIFIER'
    MONITORING = 'VIEWZOOM'
    BACKUP = 'FILE_BACKUP'
    
    # Status icons
    CONNECTED = 'CHECKMARK'
    DISCONNECTED = 'ERROR'
    WARNING = 'ERROR'
    INFO = 'INFO'
    
    # Action icons
    TEST = 'LINKED'
    INITIALIZE = 'PLAY'
    CLEAR = 'TRASH'
    RESET = 'FILE_REFRESH'

class BlendProSpacing:
    """Spacing constants for consistent layout"""
    
    # Factors for separator spacing
    SECTION = 1.0      # Between major sections
    SUBSECTION = 0.5   # Between subsections
    ITEM = 0.3         # Between items
    
    # Scale factors for UI elements
    BUTTON_SCALE = 1.2
    TAB_SCALE = 1.3
    HEADER_SCALE = 1.1

class BlendProLayout:
    """Layout utilities for consistent UI structure"""
    
    @staticmethod
    def create_header(layout, title, icon, status_text=None, status_icon=None):
        """Create a standardized header with optional status indicator"""
        header = layout.row()
        header.label(text=title, icon=icon)
        
        if status_text and status_icon:
            header.label(text=status_text, icon=status_icon)
        
        return header
    
    @staticmethod
    def create_section_box(layout, title, icon):
        """Create a standardized section box with header"""
        box = layout.box()
        BlendProLayout.create_header(box, title, icon)
        box.separator(factor=BlendProSpacing.SUBSECTION)
        return box
    
    @staticmethod
    def create_two_column_grid(layout):
        """Create a standardized two-column grid layout"""
        return layout.grid_flow(columns=2, align=True)
    
    @staticmethod
    def create_action_row(layout, scale_y=None):
        """Create a standardized action button row"""
        row = layout.row(align=True)
        if scale_y:
            row.scale_y = scale_y
        else:
            row.scale_y = BlendProSpacing.BUTTON_SCALE
        return row
    
    @staticmethod
    def create_status_indicator(layout, is_active, active_text="Active", inactive_text="Inactive"):
        """Create a standardized status indicator"""
        row = layout.row()
        row.scale_y = 0.8
        
        if is_active:
            row.label(text=active_text, icon=BlendProIcons.CONNECTED)
        else:
            row.label(text=inactive_text, icon=BlendProIcons.DISCONNECTED)
        
        return row

class BlendProValidation:
    """Validation utilities for settings"""
    
    @staticmethod
    def validate_api_key(api_key):
        """Validate API key format"""
        if not api_key:
            return False, "API key is required"
        
        if len(api_key) < 10:
            return False, "API key too short"
        
        return True, "Valid"
    
    @staticmethod
    def validate_url(url):
        """Validate URL format"""
        if not url:
            return True, "Optional"  # URLs are optional
        
        if not url.startswith(('http://', 'https://')):
            return False, "Must start with http:// or https://"
        
        return True, "Valid"
    
    @staticmethod
    def get_system_health_score(preferences):
        """Calculate overall system health score (0-100)"""
        score = 0
        
        # API configuration (40 points)
        if preferences.api_key:
            score += 30
        if preferences.vision_api_key:
            score += 10
        
        # Essential features (30 points)
        essential_features = [
            preferences.enable_vision_context,
            preferences.enable_multi_step_planning,
            preferences.enable_proactive_suggestions
        ]
        score += sum(essential_features) * 10
        
        # System features (20 points)
        system_features = [
            preferences.enable_scene_monitoring,
            preferences.enable_auto_backup,
            preferences.enable_caching
        ]
        score += sum(system_features) * 7
        
        # Performance monitoring (10 points)
        monitoring_features = [
            preferences.monitor_providers,
            preferences.monitor_agents,
            preferences.monitor_vision,
            preferences.monitor_memory
        ]
        score += sum(monitoring_features) * 2.5
        
        return min(100, int(score))

def apply_blendpro_theme(layout):
    """Apply BlendPro theme styling to a layout"""
    # This is a placeholder for future theme application
    # Blender's UI system has limited styling options
    pass

def get_health_status_color(score):
    """Get color based on health score"""
    if score >= 80:
        return BlendProColors.SUCCESS
    elif score >= 60:
        return BlendProColors.WARNING
    else:
        return BlendProColors.ERROR

def get_health_status_text(score):
    """Get status text based on health score"""
    if score >= 80:
        return "Excellent"
    elif score >= 60:
        return "Good"
    elif score >= 40:
        return "Fair"
    else:
        return "Poor"
